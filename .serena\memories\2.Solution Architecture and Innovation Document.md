# 2. 解决方案架构与创新文档 - 数据采集系统

| 版本 | 最后更新时间 | 更新者 | 变更摘要 |
| :--- | :--- | :--- | :--- |
| 1.0  | 2025-08-04 20:15:36 +08:00 | BA | 初始创建，确定分层架构方案 |

---

## 1. 方案选择与架构概述

### 1.1 选定方案：分层架构模式（方案A）

**选择理由：**
- 架构清晰，符合动态配置需求
- 层次间解耦，便于维护和扩展
- 支持未来添加modbus RTU等协议
- 开发和调试友好

### 1.2 整体架构设计

```
┌─────────────────────────────────────────────────────────┐
│                   用户界面层 (UI Layer)                  │
│  ┌─────────────────┐              ┌─────────────────┐   │
│  │   CLI Interface │              │  GUI Interface  │   │
│  │   (第一阶段)     │              │   (第二阶段)     │   │
│  └─────────────────┘              └─────────────────┘   │
└─────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────┐
│                  业务逻辑层 (Business Layer)             │
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────┐ │
│  │  Protocol Flow  │  │  Command Exec   │  │  Logger  │ │
│  │   Controller    │  │    Manager      │  │ Manager  │ │
│  └─────────────────┘  └─────────────────┘  └──────────┘ │
└─────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────┐
│                  数据处理层 (Data Layer)                 │
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────┐ │
│  │  Frame Detector │  │  Data Parser    │  │ Response │ │
│  │                 │  │                 │  │Validator │ │
│  └─────────────────┘  └─────────────────┘  └──────────┘ │
└─────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────┐
│                 通信抽象层 (Communication Layer)         │
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────┐ │
│  │ Serial Manager  │  │ Buffer Manager  │  │ Config   │ │
│  │                 │  │                 │  │ Manager  │ │
│  └─────────────────┘  └─────────────────┘  └──────────┘ │
└─────────────────────────────────────────────────────────┘
```

## 2. 详细分层设计

### 2.1 通信抽象层 (Communication Layer)

**职责：** 处理底层串口通信、数据缓冲和配置管理

#### 文件结构：
```
communication/
├── __init__.py
├── serial_manager.py      # 串口管理器
├── buffer_manager.py      # 缓冲区管理器
└── config_manager.py      # 配置管理器
```

#### 2.1.1 serial_manager.py
**功能：**
- 封装pyserial库，提供统一的串口操作接口
- 管理串口连接、断开、重连
- 独立线程处理串口数据收发
- 串口参数动态配置（波特率、数据位等）

**核心类：**
```python
class SerialManager:
    def __init__(self, config)
    def connect(self, port, **kwargs)
    def disconnect(self)
    def send_data(self, hex_string)
    def start_receiving_thread(self)
    def stop_receiving_thread(self)
```

#### 2.1.2 buffer_manager.py
**功能：**
- 实现固定大小循环缓冲区
- 处理串口接收数据的缓存
- 提供线程安全的数据读写接口
- 缓冲区状态监控和管理

**核心类：**
```python
class CircularBuffer:
    def __init__(self, size=4096)
    def write(self, data)
    def read(self, length)
    def peek(self, length)
    def get_available_data_size(self)
```

#### 2.1.3 config_manager.py
**功能：**
- JSON配置文件的加载、验证、解析
- 配置文件格式检查和错误处理
- 动态配置更新和热加载
- 配置项的类型转换和默认值处理

**核心类：**
```python
class ConfigManager:
    def __init__(self)
    def load_config(self, config_path)
    def validate_config(self, config_data)
    def get_serial_config(self)
    def get_protocol_flow(self)
    def get_commands(self)
    def get_frame_detection_config(self)
```

### 2.2 数据处理层 (Data Layer)

**职责：** 处理数据流解析、帧检测和应答验证

#### 文件结构：
```
data_processing/
├── __init__.py
├── frame_detector.py      # 帧检测器
├── data_parser.py         # 数据解析器
└── response_validator.py  # 应答验证器
```

#### 2.2.1 frame_detector.py
**功能：**
- 基于JSON配置的动态帧检测
- 处理帧头、帧尾、长度字段检测
- 处理数据粘连和分片问题
- 完整帧提取和错误帧丢弃

**核心类：**
```python
class FrameDetector:
    def __init__(self, frame_config)
    def detect_frames(self, buffer)
    def validate_frame_integrity(self, frame_data)
    def extract_complete_frames(self, raw_data)
```

#### 2.2.2 data_parser.py
**功能：**
- 基于偏移的数据解析
- 支持多种数据类型（int8/16/32, float32/64）
- 大小端字节序处理
- 缩放系数计算

**核心类：**
```python
class DataParser:
    def __init__(self, parsing_config)
    def parse_frame(self, frame_data)
    def extract_field(self, data, offset, length, data_type, endian)
    def apply_scale_factor(self, value, scale_factor)
```

#### 2.2.3 response_validator.py
**功能：**
- 精确对比验证
- 正则表达式验证
- 超时和重试机制
- 验证结果记录

**核心类：**
```python
class ResponseValidator:
    def __init__(self)
    def validate_exact(self, received, expected)
    def validate_regex(self, received, pattern)
    def validate_with_retry(self, received, validation_config)
```

### 2.3 业务逻辑层 (Business Layer)

**职责：** 协议流程控制、指令执行管理和日志管理

#### 文件结构：
```
business_logic/
├── __init__.py
├── protocol_flow_controller.py  # 协议流程控制器
├── command_executor.py          # 指令执行管理器
└── logger_manager.py            # 日志管理器
```

#### 2.3.1 protocol_flow_controller.py
**功能：**
- 基于JSON配置的动态协议流程执行
- 协议步骤的顺序控制和条件判断
- 流程状态管理和错误处理
- 支持自定义协议流程

**核心类：**
```python
class ProtocolFlowController:
    def __init__(self, flow_config, command_executor)
    def execute_protocol_flow(self)
    def execute_step(self, step_config)
    def handle_step_error(self, step, error)
```

#### 2.3.2 command_executor.py
**功能：**
- 指令的发送和应答处理
- 三种工作模式的实现（设置、查询、循环）
- 指令超时和重试逻辑
- 指令执行状态跟踪

**核心类：**
```python
class CommandExecutor:
    def __init__(self, serial_manager, response_validator)
    def execute_setup_command(self, command_config)
    def execute_query_command(self, command_config)
    def execute_continuous_command(self, command_config)
    def send_command_with_retry(self, command_config)
```

#### 2.3.3 logger_manager.py
**功能：**
- 分级日志系统实现
- 日志格式化和文件轮转
- 模块化日志记录
- 调试信息管理

**核心类：**
```python
class LoggerManager:
    def __init__(self, log_config)
    def setup_logger(self, name, level)
    def log_serial_communication(self, direction, data)
    def log_protocol_execution(self, step, status)
    def log_error(self, module, error, traceback)
```

### 2.4 用户界面层 (UI Layer)

**职责：** 用户交互、数据展示和文件输出

#### 文件结构：
```
user_interface/
├── __init__.py
├── cli_interface.py       # 命令行界面（第一阶段）
├── gui_interface.py       # GUI界面（第二阶段）
└── output_manager.py      # 输出管理器
```

#### 2.4.1 cli_interface.py
**功能：**
- 交互式配置文件选择
- 控制台数据实时显示
- 用户命令处理
- 程序状态显示

**核心类：**
```python
class CLIInterface:
    def __init__(self, protocol_controller, output_manager)
    def start_interactive_mode(self)
    def select_config_file(self)
    def display_real_time_data(self, parsed_data)
    def handle_user_commands(self)
```

#### 2.4.2 output_manager.py
**功能：**
- 控制台输出格式化
- 文件输出管理
- 数据格式转换（CSV等）
- 输出缓冲和批量写入

**核心类：**
```python
class OutputManager:
    def __init__(self, output_config)
    def output_to_console(self, data)
    def output_to_file(self, data, filename)
    def format_data(self, data, format_type)
```

## 3. 动态配置实现策略

### 3.1 配置加载流程
```
程序启动 → 用户选择JSON → 配置验证 → 各层初始化 → 协议执行
```

### 3.2 配置传递机制
- **ConfigManager** 作为配置中心，各层通过依赖注入获取配置
- 配置更新时，通过观察者模式通知相关组件
- 支持运行时配置热更新

### 3.3 动态适应性设计
- 所有协议相关的逻辑都基于配置驱动
- 帧检测、数据解析、协议流程完全可配置
- 新协议只需提供JSON配置，无需修改代码

## 4. 线程架构设计

### 4.1 线程分配
```
主线程：用户界面 + 程序控制
串口线程：数据收发 + 缓冲区写入
处理线程：帧检测 + 数据解析 + 业务逻辑
```

### 4.2 线程间通信
- **队列机制：** 使用Python的queue.Queue进行线程间数据传递
- **锁机制：** 使用threading.Lock保护共享资源
- **事件机制：** 使用threading.Event进行线程同步

## 5. 错误处理策略

### 5.1 分层错误处理
- **通信层：** 串口连接错误、数据传输错误
- **数据层：** 帧检测错误、数据解析错误
- **业务层：** 协议执行错误、指令超时错误
- **界面层：** 用户输入错误、文件操作错误

### 5.2 错误恢复机制
- **自动重试：** 可配置的重试次数和间隔
- **降级处理：** 部分功能失败时的降级策略
- **用户通知：** 友好的错误提示和处理建议

## 6. 扩展性设计

### 6.1 协议扩展
- 通过插件机制支持新协议
- 协议特定的处理逻辑可独立开发
- 配置文件格式支持协议特定扩展

### 6.2 界面扩展
- CLI和GUI共享业务逻辑层
- 界面层可独立开发和测试
- 支持第三方界面集成

## 7. 递进输出 (为模式3技术架构设计提供方案基础)

### 7.1 为SA角色提供的关键信息
| 输出类别 | 具体内容 | 技术实现影响 | 优先级 |
| :--- | :--- | :--- | :--- |
| 架构模式 | 四层分层架构，职责清晰分离 | 技术栈选择和模块设计 | 高 |
| 动态配置 | JSON驱动的完全可配置架构 | 配置管理和验证机制设计 | 高 |
| 线程模型 | 三线程架构，队列通信机制 | 并发控制和性能优化 | 高 |
| 错误处理 | 分层错误处理和恢复机制 | 异常处理和日志设计 | 中 |
| 扩展机制 | 插件化协议支持和界面扩展 | 模块化设计和接口定义 | 中 |

### 7.2 需要SA角色重点设计的技术细节
1. **具体的类设计和接口定义**
2. **线程间通信的详细实现机制**
3. **配置验证和错误处理的具体算法**
4. **性能优化和内存管理策略**
5. **模块间依赖关系和初始化顺序**

**递进关系说明:** 本文档作为模式2的产出，基于模式1的需求分析，为模式3的技术架构设计提供完整的解决方案基础，确保SA角色能够基于确定的架构方案进行详细的技术设计。