{"version": "2.0.0", "source": "project", "metadata": {"version": "2.0.0", "description": "project 级资源注册表", "createdAt": "2025-08-04T12:02:43.021Z", "updatedAt": "2025-08-04T12:02:43.023Z", "resourceCount": 8}, "resources": [{"id": "business-analyst", "source": "project", "protocol": "role", "name": "Business Analyst 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/business-analyst/business-analyst.role.md", "metadata": {"createdAt": "2025-08-04T12:02:43.022Z", "updatedAt": "2025-08-04T12:02:43.022Z", "scannedAt": "2025-08-04T12:02:43.022Z", "path": "role/business-analyst/business-analyst.role.md"}}, {"id": "context-manager", "source": "project", "protocol": "role", "name": "Context Manager 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/context-manager/context-manager.role.md", "metadata": {"createdAt": "2025-08-04T12:02:43.022Z", "updatedAt": "2025-08-04T12:02:43.022Z", "scannedAt": "2025-08-04T12:02:43.022Z", "path": "role/context-manager/context-manager.role.md"}}, {"id": "context-management", "source": "project", "protocol": "execution", "name": "Context Management 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/context-manager/execution/context-management.execution.md", "metadata": {"createdAt": "2025-08-04T12:02:43.022Z", "updatedAt": "2025-08-04T12:02:43.022Z", "scannedAt": "2025-08-04T12:02:43.022Z", "path": "role/context-manager/execution/context-management.execution.md"}}, {"id": "context-analysis", "source": "project", "protocol": "thought", "name": "Context Analysis 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/context-manager/thought/context-analysis.thought.md", "metadata": {"createdAt": "2025-08-04T12:02:43.022Z", "updatedAt": "2025-08-04T12:02:43.022Z", "scannedAt": "2025-08-04T12:02:43.022Z", "path": "role/context-manager/thought/context-analysis.thought.md"}}, {"id": "lead-developer", "source": "project", "protocol": "role", "name": "Lead Developer 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/lead-developer/lead-developer.role.md", "metadata": {"createdAt": "2025-08-04T12:02:43.022Z", "updatedAt": "2025-08-04T12:02:43.022Z", "scannedAt": "2025-08-04T12:02:43.022Z", "path": "role/lead-developer/lead-developer.role.md"}}, {"id": "planning-expert", "source": "project", "protocol": "role", "name": "Planning Expert 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/planning-expert/planning-expert.role.md", "metadata": {"createdAt": "2025-08-04T12:02:43.023Z", "updatedAt": "2025-08-04T12:02:43.023Z", "scannedAt": "2025-08-04T12:02:43.023Z", "path": "role/planning-expert/planning-expert.role.md"}}, {"id": "project-manager", "source": "project", "protocol": "role", "name": "Project Manager 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/project-manager/project-manager.role.md", "metadata": {"createdAt": "2025-08-04T12:02:43.023Z", "updatedAt": "2025-08-04T12:02:43.023Z", "scannedAt": "2025-08-04T12:02:43.023Z", "path": "role/project-manager/project-manager.role.md"}}, {"id": "solution-architect", "source": "project", "protocol": "role", "name": "Solution Architect 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/solution-architect/solution-architect.role.md", "metadata": {"createdAt": "2025-08-04T12:02:43.023Z", "updatedAt": "2025-08-04T12:02:43.023Z", "scannedAt": "2025-08-04T12:02:43.023Z", "path": "role/solution-architect/solution-architect.role.md"}}], "stats": {"totalResources": 8, "byProtocol": {"role": 6, "execution": 1, "thought": 1}, "bySource": {"project": 8}}}