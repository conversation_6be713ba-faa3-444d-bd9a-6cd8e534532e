# 3. 技术栈与设计文档 - 数据采集系统

| 版本 | 最后更新时间 | 更新者 | 变更摘要 |
| :--- | :--- | :--- | :--- |
| 1.0  | 2025-08-05 14:28:47 +08:00 | SA | 初始创建，基于需求和方案设计技术栈 |

---

## 1. 技术栈选择与架构设计

### 1.1 核心技术栈

#### 1.1.1 编程语言与运行环境
| 技术组件 | 选择方案 | 版本要求 | 选择理由 |
| :--- | :--- | :--- | :--- |
| **主要语言** | Python | 3.8+ | 丰富的串口通信库、快速开发、跨平台支持 |
| **运行环境** | CPython | 3.8+ | 标准Python解释器，生态系统完善 |
| **包管理** | pip + requirements.txt | - | 标准Python包管理，依赖管理简单 |

#### 1.1.2 核心依赖库
| 库名称 | 版本 | 用途 | 关键特性 |
| :--- | :--- | :--- | :--- |
| **pyserial** | >=3.5 | 串口通信 | 跨平台串口支持、稳定可靠 |
| **struct** | 内置 | 二进制数据处理 | 高效的字节序转换和数据解析 |
| **threading** | 内置 | 多线程处理 | 原生线程支持、线程同步机制 |
| **queue** | 内置 | 线程间通信 | 线程安全的队列实现 |
| **json** | 内置 | 配置文件处理 | 标准JSON解析和验证 |
| **re** | 内置 | 正则表达式 | 应答报文验证支持 |
| **logging** | 内置 | 日志系统 | 分级日志、轮转支持 |
| **pathlib** | 内置 | 文件路径处理 | 现代化路径操作 |
| **dataclasses** | 内置 | 数据结构定义 | 简化配置和数据对象定义 |

#### 1.1.3 第二阶段扩展库
| 库名称 | 版本 | 用途 | 备注 |
| :--- | :--- | :--- | :--- |
| **PyQt5/6** | >=5.15/6.2 | GUI界面开发 | 第二阶段GUI开发 |
| **PyInstaller** | >=4.0 | 程序打包 | 生成独立可执行文件 |

### 1.2 架构设计原则

#### 1.2.1 设计模式选择
| 设计模式 | 应用场景 | 实现方式 | 优势 |
| :--- | :--- | :--- | :--- |
| **分层架构** | 整体系统架构 | 五层分层设计 | 职责清晰、易于维护 |
| **工厂模式** | 协议解析器创建 | 动态协议解析器工厂 | 支持协议扩展 |
| **观察者模式** | 事件通知机制 | 错误处理、状态变更通知 | 解耦组件间依赖 |
| **策略模式** | 数据验证方式 | 精确匹配vs正则表达式 | 灵活的验证策略 |
| **单例模式** | 配置管理器 | ConfigManager单例 | 全局配置访问 |

#### 1.2.2 SOLID原则应用
- **单一职责原则(SRP)：** 每个类只负责一个功能领域
- **开闭原则(OCP)：** 通过接口和抽象类支持扩展
- **里氏替换原则(LSP)：** 协议解析器可互相替换
- **接口隔离原则(ISP)：** 定义最小化的接口
- **依赖倒置原则(DIP)：** 依赖抽象而非具体实现

## 2. 详细系统架构设计

### 2.1 五层架构技术实现

#### 2.1.1 工具辅助层 (Utils Layer)
```python
# 技术实现架构
utils/
├── __init__.py                 # 包初始化
├── config_manager.py          # 配置管理核心
├── helper_utils.py            # 工具函数集合
├── constants.py               # 系统常量定义
├── validators.py              # 数据验证器
└── exceptions.py              # 自定义异常类
```

**核心技术特性：**
- **配置管理：** 基于dataclass的配置对象，JSON Schema验证
- **工具函数：** 十六进制处理、数据转换、时间格式化
- **验证机制：** 多层次配置验证，类型检查和范围验证
- **异常处理：** 自定义异常层次结构，错误分类管理

#### 2.1.2 通信抽象层 (Communication Layer)
```python
# 技术实现架构
communication/
├── __init__.py                 # 包初始化
├── serial_manager.py          # 串口管理器
├── buffer_manager.py          # 循环缓冲区管理
├── connection_pool.py         # 连接池管理
└── protocols/                 # 协议适配器
    ├── __init__.py
    └── base_protocol.py       # 协议基类
```

**核心技术特性：**
- **串口管理：** pyserial封装，连接池管理，自动重连机制
- **缓冲区设计：** 固定大小循环缓冲区，线程安全操作
- **协议适配：** 抽象协议接口，支持协议扩展
- **连接管理：** 连接状态监控，异常恢复机制

#### 2.1.3 数据处理层 (Data Layer)
```python
# 技术实现架构
data_processing/
├── __init__.py                 # 包初始化
├── frame_detector.py          # 帧检测引擎
├── data_parser.py             # 数据解析引擎
├── response_validator.py      # 应答验证器
├── queue_manager.py           # 队列管理器
└── parsers/                   # 数据解析器
    ├── __init__.py
    ├── binary_parser.py       # 二进制数据解析
    └── protocol_parser.py     # 协议特定解析
```

**核心技术特性：**
- **帧检测算法：** 状态机实现，支持复杂帧结构
- **数据解析引擎：** struct模块优化，支持多种数据类型
- **队列管理：** 优先级队列，批量处理机制
- **验证策略：** 可插拔验证器，支持精确匹配和正则表达式

#### 2.1.4 业务逻辑层 (Business Layer)
```python
# 技术实现架构
business_logic/
├── __init__.py                 # 包初始化
├── protocol_flow_controller.py # 协议流程控制
├── command_executor.py         # 指令执行器
├── error_handler.py            # 错误处理器
├── logger_manager.py           # 日志管理器
└── state_machine.py            # 状态机实现
```

**核心技术特性：**
- **流程控制：** 基于状态机的协议流程管理
- **指令执行：** 异步指令执行，超时和重试机制
- **错误处理：** 分级错误处理，智能恢复策略
- **日志管理：** 结构化日志，性能监控集成

#### 2.1.5 用户界面层 (UI Layer)
```python
# 技术实现架构
user_interface/
├── __init__.py                 # 包初始化
├── cli_interface.py           # 命令行界面
├── gui_interface.py           # GUI界面（第二阶段）
├── output_manager.py          # 输出管理器
└── formatters/                # 数据格式化器
    ├── __init__.py
    ├── console_formatter.py   # 控制台格式化
    └── file_formatter.py      # 文件格式化
```

**核心技术特性：**
- **CLI设计：** 交互式命令行，实时数据显示
- **输出管理：** 多格式输出支持，异步文件写入
- **数据格式化：** 可配置的数据展示格式
- **GUI架构：** MVC模式，事件驱动设计（第二阶段）

### 2.2 核心技术组件设计

#### 2.2.1 配置管理系统
```python
@dataclass
class SerialConfig:
    port: str
    baudrate: int = 9600
    databits: int = 8
    parity: str = "none"
    stopbits: int = 1
    timeout: float = 1.0

@dataclass
class ProtocolConfig:
    protocol_info: dict
    serial_config: SerialConfig
    protocol_flow: dict
    commands: dict
    continuous_data: dict = None

class ConfigManager:
    """配置管理器 - 单例模式"""
    _instance = None
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance
    
    def load_config(self, config_path: Path) -> ProtocolConfig:
        """加载并验证配置文件"""
        pass
    
    def validate_config(self, config_data: dict) -> bool:
        """配置文件验证"""
        pass
```

#### 2.2.2 循环缓冲区实现
```python
class CircularBuffer:
    """线程安全的循环缓冲区"""
    
    def __init__(self, size: int = 4096):
        self._buffer = bytearray(size)
        self._size = size
        self._head = 0
        self._tail = 0
        self._lock = threading.RLock()
        self._not_empty = threading.Condition(self._lock)
        self._not_full = threading.Condition(self._lock)
    
    def write(self, data: bytes) -> int:
        """写入数据，返回实际写入字节数"""
        with self._lock:
            # 实现循环写入逻辑
            pass
    
    def read(self, size: int) -> bytes:
        """读取数据，阻塞直到有数据可读"""
        with self._not_empty:
            # 实现循环读取逻辑
            pass
    
    def peek(self, size: int) -> bytes:
        """预览数据，不移动读指针"""
        with self._lock:
            # 实现预览逻辑
            pass
```

#### 2.2.3 帧检测算法
```python
class FrameDetector:
    """基于状态机的帧检测器"""
    
    def __init__(self, frame_config: dict):
        self.header = bytes.fromhex(frame_config['header'].replace(' ', ''))
        self.tail = bytes.fromhex(frame_config['tail'].replace(' ', ''))
        self.min_length = frame_config['min_length']
        self.max_length = frame_config['max_length']
        self._state = 'SEARCHING_HEADER'
        self._frame_buffer = bytearray()
    
    def detect_frames(self, data: bytes) -> List[bytes]:
        """检测完整帧，返回帧列表"""
        frames = []
        for byte in data:
            frame = self._process_byte(byte)
            if frame:
                frames.append(frame)
        return frames
    
    def _process_byte(self, byte: int) -> Optional[bytes]:
        """状态机处理单个字节"""
        if self._state == 'SEARCHING_HEADER':
            # 搜索帧头逻辑
            pass
        elif self._state == 'COLLECTING_DATA':
            # 收集数据逻辑
            pass
        elif self._state == 'VALIDATING_TAIL':
            # 验证帧尾逻辑
            pass
```

#### 2.2.4 数据解析引擎
```python
class DataParser:
    """高性能数据解析引擎"""
    
    def __init__(self, parsing_config: List[dict]):
        self._parsers = []
        for field_config in parsing_config:
            parser = self._create_field_parser(field_config)
            self._parsers.append(parser)
    
    def parse_frame(self, frame_data: bytes) -> Dict[str, Any]:
        """解析帧数据，返回字段字典"""
        result = {}
        for parser in self._parsers:
            try:
                field_name, field_value = parser.parse(frame_data)
                result[field_name] = field_value
            except Exception as e:
                logger.error(f"解析字段失败: {e}")
                result[parser.name] = None
        return result
    
    def _create_field_parser(self, config: dict) -> 'FieldParser':
        """创建字段解析器"""
        return FieldParser(
            name=config['name'],
            offset=config['offset'],
            length=config['length'],
            data_type=config['data_type'],
            endian=config['endian'],
            scale_factor=config['scale_factor'],
            unit=config.get('unit', '')
        )

class FieldParser:
    """单个字段解析器"""
    
    def __init__(self, name: str, offset: int, length: int, 
                 data_type: str, endian: str, scale_factor: float, unit: str):
        self.name = name
        self.offset = offset
        self.length = length
        self.data_type = data_type
        self.endian = endian
        self.scale_factor = scale_factor
        self.unit = unit
        self._struct_format = self._build_struct_format()
    
    def parse(self, data: bytes) -> Tuple[str, Any]:
        """解析字段值"""
        if len(data) < self.offset + self.length:
            raise ValueError(f"数据长度不足，需要{self.offset + self.length}字节")
        
        field_data = data[self.offset:self.offset + self.length]
        raw_value = struct.unpack(self._struct_format, field_data)[0]
        scaled_value = raw_value * self.scale_factor
        
        return self.name, scaled_value
    
    def _build_struct_format(self) -> str:
        """构建struct格式字符串"""
        endian_char = '<' if self.endian == 'little' else '>'
        type_char = {
            'int8': 'b', 'int16': 'h', 'int32': 'i',
            'float32': 'f', 'float64': 'd'
        }[self.data_type]
        return f"{endian_char}{type_char}"
```

### 2.3 多线程架构设计

#### 2.3.1 线程模型
```python
class DataAcquisitionSystem:
    """数据采集系统主控制器"""
    
    def __init__(self, config: ProtocolConfig):
        self.config = config
        self._serial_thread = None
        self._processing_thread = None
        self._main_thread = threading.current_thread()
        
        # 线程间通信队列
        self._raw_data_queue = queue.Queue(maxsize=1000)
        self._parsed_data_queue = queue.Queue(maxsize=500)
        self._command_queue = queue.Queue(maxsize=100)
        
        # 线程同步事件
        self._shutdown_event = threading.Event()
        self._pause_event = threading.Event()
    
    def start(self):
        """启动系统"""
        self._serial_thread = threading.Thread(
            target=self._serial_worker,
            name="SerialThread",
            daemon=True
        )
        self._processing_thread = threading.Thread(
            target=self._processing_worker,
            name="ProcessingThread",
            daemon=True
        )
        
        self._serial_thread.start()
        self._processing_thread.start()
    
    def _serial_worker(self):
        """串口工作线程"""
        serial_manager = SerialManager(self.config.serial_config)
        buffer_manager = BufferManager()
        
        try:
            serial_manager.connect()
            while not self._shutdown_event.is_set():
                # 处理命令队列
                self._process_commands(serial_manager)
                
                # 读取串口数据
                data = serial_manager.read_available()
                if data:
                    buffer_manager.write(data)
                    
                    # 检测完整帧
                    frames = self._detect_frames(buffer_manager)
                    for frame in frames:
                        self._raw_data_queue.put(frame, timeout=0.1)
                
                time.sleep(0.001)  # 1ms循环间隔
                
        except Exception as e:
            logger.error(f"串口线程异常: {e}")
        finally:
            serial_manager.disconnect()
    
    def _processing_worker(self):
        """数据处理工作线程"""
        data_parser = DataParser(self.config.continuous_data['data_parsing'])
        
        try:
            while not self._shutdown_event.is_set():
                try:
                    # 批量处理数据
                    frames = []
                    for _ in range(10):  # 批量大小
                        frame = self._raw_data_queue.get(timeout=0.1)
                        frames.append(frame)
                    
                    # 解析数据
                    for frame in frames:
                        parsed_data = data_parser.parse_frame(frame)
                        self._parsed_data_queue.put(parsed_data, timeout=0.1)
                        
                except queue.Empty:
                    continue
                except Exception as e:
                    logger.error(f"数据处理异常: {e}")
                    
        except Exception as e:
            logger.error(f"处理线程异常: {e}")
```

#### 2.3.2 线程安全机制
- **队列通信：** 使用queue.Queue进行线程间数据传递
- **锁机制：** RLock用于复杂的同步场景
- **条件变量：** Condition用于线程间协调
- **事件机制：** Event用于线程状态控制

### 2.4 性能优化设计

#### 2.4.1 内存优化
```python
class ObjectPool:
    """对象池 - 减少内存分配"""
    
    def __init__(self, factory_func, max_size=100):
        self._factory = factory_func
        self._pool = queue.Queue(maxsize=max_size)
        self._max_size = max_size
    
    def get(self):
        """获取对象"""
        try:
            return self._pool.get_nowait()
        except queue.Empty:
            return self._factory()
    
    def put(self, obj):
        """归还对象"""
        try:
            # 重置对象状态
            if hasattr(obj, 'reset'):
                obj.reset()
            self._pool.put_nowait(obj)
        except queue.Full:
            pass  # 池满时丢弃对象

# 预编译正则表达式
class RegexCache:
    """正则表达式缓存"""
    _cache = {}
    
    @classmethod
    def get_pattern(cls, pattern_str: str) -> re.Pattern:
        if pattern_str not in cls._cache:
            cls._cache[pattern_str] = re.compile(pattern_str)
        return cls._cache[pattern_str]
```

#### 2.4.2 算法优化
- **批量处理：** 减少线程切换开销
- **预编译：** 正则表达式和struct格式预编译
- **内存池：** 对象复用减少GC压力
- **缓存机制：** 配置和解析结果缓存

## 3. 安全架构设计

### 3.1 数据安全
- **输入验证：** 所有外部输入严格验证
- **缓冲区保护：** 防止缓冲区溢出
- **异常处理：** 安全的异常处理，避免信息泄露
- **日志安全：** 敏感信息脱敏处理

### 3.2 系统安全
- **权限控制：** 最小权限原则
- **资源限制：** 内存和CPU使用限制
- **错误恢复：** 安全的错误恢复机制
- **配置保护：** 配置文件完整性检查

**递进关系说明:** 本文档作为模式3的产出，基于模式1的需求分析和模式2的解决方案设计，提供了完整的技术栈选择和详细的系统架构设计，为模式4的项目规划提供了技术实现基础。