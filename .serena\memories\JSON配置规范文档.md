# JSON配置规范文档 - 数据采集系统

| 版本 | 最后更新时间 | 更新者 | 变更摘要 |
| :--- | :--- | :--- | :--- |
| 1.0  | 2025-08-05 09:08:59 +08:00 | BA | 初始创建，整合两种JSON配置规范 |

---

## 1. 配置文件概述

数据采集系统使用两种类型的JSON配置文件：

1. **自由串口协议配置文件** - 用户运行时动态加载的协议定义文件
2. **系统运行参数配置文件** - 系统级运行参数和性能配置文件

## 2. 自由串口协议配置文件

### 2.1 配置文件用途
- 定义具体的串口协议规则和流程
- 用户在程序运行时通过交互方式选择加载
- 支持任意自定义的串口协议，无需硬编码

### 2.2 完整配置文件结构

```json
{
  "protocol_info": {
    "name": "自定义协议名称",
    "description": "协议描述",
    "version": "1.0"
  },
  "serial_config": {
    "baudrate": 9600,
    "databits": 8,
    "parity": "none",
    "stopbits": 1,
    "timeout": 1.0
  },
  "protocol_flow": {
    "steps": [
      {
        "name": "初始化配置",
        "type": "setup",
        "commands": ["init_cmd1", "init_cmd2"]
      },
      {
        "name": "属性查询",
        "type": "query", 
        "commands": ["query_cmd1"]
      },
      {
        "name": "循环查询激活",
        "type": "continuous_start",
        "commands": ["start_continuous"]
      }
    ]
  },
  "commands": {
    "setup": [
      {
        "id": "init_cmd1",
        "name": "初始化命令",
        "send": "01 03 00 00 00 01 84 0A",
        "response_validation": {
          "type": "exact",
          "pattern": "01 03 02 00 01 79 84",
          "timeout": 2.0,
          "retry_count": 3
        }
      }
    ],
    "query": [
      {
        "id": "query_cmd1",
        "name": "查询温度",
        "send": "01 03 00 01 00 01 D5 CA",
        "response_validation": {
          "type": "regex",
          "pattern": "01 03 02 [0-9A-F]{4} [0-9A-F]{4}",
          "timeout": 1.0,
          "retry_count": 2
        }
      }
    ],
    "continuous": [
      {
        "id": "start_continuous",
        "name": "启动循环查询",
        "send": "01 04 00 00 00 02 71 CB",
        "response_validation": {
          "type": "exact",
          "pattern": "01 04 04",
          "timeout": 1.0,
          "retry_count": 1
        }
      }
    ]
  },
  "continuous_data": {
    "frame_detection": {
      "header": "AA BB",
      "tail": "CC DD",
      "min_length": 8,
      "max_length": 256,
      "length_field": {
        "offset": 2,
        "length": 1,
        "includes_header": true
      }
    },
    "data_parsing": [
      {
        "name": "温度",
        "offset": 4,
        "length": 2,
        "data_type": "int16",
        "endian": "big",
        "scale_factor": 0.1,
        "unit": "°C"
      },
      {
        "name": "湿度",
        "offset": 6,
        "length": 2,
        "data_type": "int16",
        "endian": "big",
        "scale_factor": 0.01,
        "unit": "%RH"
      }
    ]
  }
}
```

### 2.3 配置字段详细说明

#### 2.3.1 protocol_info（协议信息）
| 字段 | 类型 | 必需 | 说明 |
| :--- | :--- | :--- | :--- |
| name | string | 是 | 协议名称，用于标识协议 |
| description | string | 否 | 协议描述信息 |
| version | string | 否 | 协议版本号 |

#### 2.3.2 serial_config（串口配置）
| 字段 | 类型 | 必需 | 说明 | 可选值 |
| :--- | :--- | :--- | :--- | :--- |
| baudrate | integer | 是 | 波特率 | 9600, 19200, 38400, 57600, 115200等 |
| databits | integer | 是 | 数据位 | 5, 6, 7, 8 |
| parity | string | 是 | 校验位 | "none", "even", "odd", "mark", "space" |
| stopbits | integer | 是 | 停止位 | 1, 1.5, 2 |
| timeout | float | 是 | 超时时间（秒） | >0 |

#### 2.3.3 protocol_flow（协议流程）
| 字段 | 类型 | 必需 | 说明 |
| :--- | :--- | :--- | :--- |
| steps | array | 是 | 协议执行步骤数组 |

**steps数组元素：**
| 字段 | 类型 | 必需 | 说明 | 可选值 |
| :--- | :--- | :--- | :--- | :--- |
| name | string | 是 | 步骤名称 | - |
| type | string | 是 | 步骤类型 | "setup", "query", "continuous_start" |
| commands | array | 是 | 该步骤要执行的命令ID列表 | - |

#### 2.3.4 commands（指令定义）
包含三种类型的指令：setup（设置）、query（查询）、continuous（循环）

**指令对象结构：**
| 字段 | 类型 | 必需 | 说明 |
| :--- | :--- | :--- | :--- |
| id | string | 是 | 指令唯一标识符 |
| name | string | 是 | 指令名称 |
| send | string | 是 | 发送的十六进制指令（空格分隔） |
| response_validation | object | 是 | 应答验证配置 |

**response_validation对象：**
| 字段 | 类型 | 必需 | 说明 | 可选值 |
| :--- | :--- | :--- | :--- | :--- |
| type | string | 是 | 验证类型 | "exact", "regex" |
| pattern | string | 是 | 验证模式（精确匹配或正则表达式） | - |
| timeout | float | 是 | 超时时间（秒） | >0 |
| retry_count | integer | 是 | 重试次数 | ≥0 |

#### 2.3.5 continuous_data（循环数据配置）

**frame_detection（帧检测配置）：**
| 字段 | 类型 | 必需 | 说明 |
| :--- | :--- | :--- | :--- |
| header | string | 是 | 帧头（十六进制，空格分隔） |
| tail | string | 是 | 帧尾（十六进制，空格分隔） |
| min_length | integer | 是 | 最小帧长度 |
| max_length | integer | 是 | 最大帧长度 |
| length_field | object | 否 | 长度字段配置 |

**length_field对象：**
| 字段 | 类型 | 必需 | 说明 |
| :--- | :--- | :--- | :--- |
| offset | integer | 是 | 长度字段在帧中的偏移 |
| length | integer | 是 | 长度字段本身的字节数 |
| includes_header | boolean | 是 | 长度是否包含帧头 |

**data_parsing（数据解析配置）：**
| 字段 | 类型 | 必需 | 说明 | 可选值 |
| :--- | :--- | :--- | :--- | :--- |
| name | string | 是 | 数据字段名称 | - |
| offset | integer | 是 | 数据在帧中的偏移 | ≥0 |
| length | integer | 是 | 数据字段长度（字节） | >0 |
| data_type | string | 是 | 数据类型 | "int8", "int16", "int32", "float32", "float64" |
| endian | string | 是 | 字节序 | "big", "little" |
| scale_factor | float | 是 | 缩放系数 | - |
| unit | string | 否 | 数据单位 | - |

## 3. 系统运行参数配置文件

### 3.1 配置文件用途
- 定义系统级运行参数和性能配置
- 控制错误处理、队列管理、性能优化等系统行为
- 通常在程序启动时加载，运行期间不变

### 3.2 完整配置文件结构

```json
{
  "error_handling": {
    "continuous_mode": {
      "max_consecutive_errors": 5,
      "pause_on_max_errors": true,
      "recovery_strategy": "manual",
      "error_types": {
        "frame_error": {"weight": 1},
        "parse_error": {"weight": 1},
        "comm_error": {"weight": 2}
      }
    }
  },
  "queue_config": {
    "response_queue_size": 100,
    "queue_warning_threshold": 0.8,
    "batch_processing_size": 10
  },
  "performance": {
    "buffer_size": 4096,
    "processing_timeout": 0.1,
    "max_processing_threads": 2
  },
  "logging": {
    "level": "INFO",
    "file_max_size": "10MB",
    "file_backup_count": 30,
    "format": "[%(asctime)s] [%(levelname)s] [%(name)s] - %(message)s"
  },
  "monitoring": {
    "enable_performance_monitoring": true,
    "monitoring_interval": 1.0,
    "alert_thresholds": {
      "cpu_usage": 80,
      "memory_usage": 80,
      "queue_usage": 90
    }
  }
}
```

### 3.3 系统配置字段详细说明

#### 3.3.1 error_handling（错误处理配置）

**continuous_mode（循环模式错误处理）：**
| 字段 | 类型 | 必需 | 说明 | 默认值 |
| :--- | :--- | :--- | :--- | :--- |
| max_consecutive_errors | integer | 是 | 最大连续错误次数 | 5 |
| pause_on_max_errors | boolean | 是 | 达到最大错误时是否暂停 | true |
| recovery_strategy | string | 是 | 恢复策略 | "manual", "auto_retry" |
| error_types | object | 否 | 错误类型权重配置 | - |

**error_types对象：**
| 字段 | 类型 | 说明 | 权重建议 |
| :--- | :--- | :--- | :--- |
| frame_error | object | 帧检测错误 | weight: 1 |
| parse_error | object | 数据解析错误 | weight: 1 |
| comm_error | object | 通信错误 | weight: 2 |

#### 3.3.2 queue_config（队列配置）
| 字段 | 类型 | 必需 | 说明 | 默认值 |
| :--- | :--- | :--- | :--- | :--- |
| response_queue_size | integer | 是 | 应答队列大小 | 100 |
| queue_warning_threshold | float | 是 | 队列警告阈值（0-1） | 0.8 |
| batch_processing_size | integer | 是 | 批处理大小 | 10 |

#### 3.3.3 performance（性能配置）
| 字段 | 类型 | 必需 | 说明 | 默认值 |
| :--- | :--- | :--- | :--- | :--- |
| buffer_size | integer | 是 | 缓冲区大小（字节） | 4096 |
| processing_timeout | float | 是 | 处理超时时间（秒） | 0.1 |
| max_processing_threads | integer | 是 | 最大处理线程数 | 2 |

#### 3.3.4 logging（日志配置）
| 字段 | 类型 | 必需 | 说明 | 可选值 |
| :--- | :--- | :--- | :--- | :--- |
| level | string | 是 | 日志级别 | "DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL" |
| file_max_size | string | 是 | 日志文件最大大小 | "10MB", "50MB"等 |
| file_backup_count | integer | 是 | 备份文件数量 | >0 |
| format | string | 是 | 日志格式 | - |

#### 3.3.5 monitoring（监控配置）
| 字段 | 类型 | 必需 | 说明 | 默认值 |
| :--- | :--- | :--- | :--- | :--- |
| enable_performance_monitoring | boolean | 是 | 是否启用性能监控 | true |
| monitoring_interval | float | 是 | 监控间隔（秒） | 1.0 |
| alert_thresholds | object | 是 | 告警阈值配置 | - |

**alert_thresholds对象：**
| 字段 | 类型 | 说明 | 单位 |
| :--- | :--- | :--- | :--- |
| cpu_usage | integer | CPU使用率告警阈值 | 百分比 |
| memory_usage | integer | 内存使用率告警阈值 | 百分比 |
| queue_usage | integer | 队列使用率告警阈值 | 百分比 |

## 4. 配置文件使用说明

### 4.1 自由串口协议配置文件
- **存放位置：** `config/protocols/` 目录下
- **命名规范：** `{协议名称}.json`
- **加载方式：** 程序启动时用户交互选择
- **验证要求：** 启动时进行完整性和格式验证

### 4.2 系统运行参数配置文件
- **存放位置：** `config/` 目录下
- **文件名：** `system_config.json`
- **加载方式：** 程序启动时自动加载
- **默认处理：** 缺失字段使用默认值

### 4.3 配置文件验证规则

#### 4.3.1 必需字段验证
- 所有标记为"必需"的字段都必须存在
- 字段类型必须与规范一致
- 数值字段必须在合理范围内

#### 4.3.2 逻辑一致性验证
- protocol_flow中引用的command ID必须在commands中存在
- 数据解析的offset和length不能超出帧长度范围
- 串口参数必须是硬件支持的值

#### 4.3.3 格式验证
- 十六进制字符串必须是有效格式（如："01 03 00 00"）
- 正则表达式必须是有效的Python正则表达式
- 文件大小格式必须正确（如："10MB"）

## 5. 配置示例和模板

### 5.1 温湿度传感器协议示例
```json
{
  "protocol_info": {
    "name": "温湿度传感器协议",
    "description": "标准温湿度传感器Modbus协议",
    "version": "1.0"
  },
  "serial_config": {
    "baudrate": 9600,
    "databits": 8,
    "parity": "none",
    "stopbits": 1,
    "timeout": 1.0
  }
  // ... 其他配置
}
```

### 5.2 高性能系统配置示例
```json
{
  "queue_config": {
    "response_queue_size": 500,
    "queue_warning_threshold": 0.7,
    "batch_processing_size": 20
  },
  "performance": {
    "buffer_size": 8192,
    "processing_timeout": 0.05,
    "max_processing_threads": 4
  }
  // ... 其他配置
}
```

## 6. 配置文件最佳实践

### 6.1 协议配置最佳实践
1. **命名规范：** 使用描述性的协议名称和指令名称
2. **超时设置：** 根据设备响应特性合理设置超时时间
3. **重试策略：** 根据通信可靠性要求设置重试次数
4. **数据解析：** 确保偏移量和长度的准确性

### 6.2 系统配置最佳实践
1. **性能调优：** 根据硬件性能调整缓冲区和线程数
2. **错误处理：** 根据应用场景设置合适的错误阈值
3. **监控配置：** 启用性能监控便于问题诊断
4. **日志管理：** 合理设置日志级别和轮转策略

## 7. 故障排除

### 7.1 常见配置错误
1. **JSON格式错误：** 检查括号、引号、逗号是否正确
2. **字段类型错误：** 确保数值字段不使用字符串
3. **引用错误：** 检查command ID引用是否存在
4. **范围错误：** 确保数值在合理范围内

### 7.2 配置验证工具
系统提供配置验证功能，启动时会自动检查：
- JSON格式正确性
- 必需字段完整性
- 字段类型一致性
- 逻辑关系正确性

---

**文档维护说明：** 本文档随系统功能更新而更新，请确保使用最新版本的配置规范。