# 1. 需求与规范文档 - 数据采集系统

| 版本 | 最后更新时间 | 更新者 | 变更摘要 |
| :--- | :--- | :--- | :--- |
| 1.0  | 2025-01-08 | PM | 初始创建 |
| 1.1  | 2025-01-08 | PM | 基于用户反馈的详细需求分析 |
| 1.2  | 2025-01-08 | PM | 完善数据流处理、线程架构、用户界面等关键需求 |
| 1.3  | 2025-01-08 | PM | 确认核心技术方案，完善JSON配置结构 |

---

## 1. 项目概述

### 1.1 项目目标
开发一个支持多种硬件协议的数据采集系统，具备高度的动态配置能力，通过JSON配置文件实现任意自由串口协议的支持，无需硬编码。

### 1.2 项目范围
* **范围内 (In Scope):**
  * 自由串口协议支持（优先开发）
  * modbus RTU协议支持（后续扩展）
  * 基于pyserial的串口通信
  * JSON配置文件动态加载
  * 三种工作模式：设置、单次查询、循环查询
  * 十六进制数据处理
  * 精确的指令应答验证
  * 基于偏移的数据解析
  * 命令行界面（优先）+ PyQt GUI界面（后续）
  * 分级日志系统
  * PyInstaller程序打包
* **范围外 (Out of Scope):**
  * 其他通信协议（如TCP/IP、USB等）
  * 复杂的嵌套数据结构解析
  * 实时数据可视化界面
  * 网络远程配置功能

## 2. 详细功能需求

### 2.1 串口协议支持需求
* **FR001: 自由串口协议支持**
  * **描述:** 支持任意自定义的串口协议，无标准帧结构限制
  * **数据格式:** 全部使用十六进制字符串配置（如："01 03 00 00 00 01 84 0A"）
  * **帧结构:** 每种协议自定义，包含帧头、帧尾、校验数据、报文数据的完整指令
  * **字节序:** 支持大端(big)和小端(little)，在配置中指定

* **FR002: 三种工作模式**
  * **设置模式:** 向从机发送单次配置指令
  * **单次查询:** 向从机发送单次查询指令
  * **循环查询:** 从机持续向主机发送数据报文，软件进行解析

### 2.2 应答报文验证需求
* **FR003: 多种验证方式**
  * **精确对比:** 按字节完全匹配应答报文
  * **正则表达式:** 支持十六进制正则表达式匹配，在JSON中配置正则表达式
  * **超时重试:** 可配置超时时间和重试次数
  * **失败处理:** 超过重试次数后直接报错，提示用户

### 2.3 数据流处理需求
* **FR004: 串口数据流处理**
  * **流式数据处理:** 处理串口通信的流式数据特性
  * **数据完整性检查:** 基于帧头、帧尾和长度三方面校验
  * **分片数据处理:** 解决数据分片接收问题
  * **数据粘连处理:** 解决多帧数据粘连问题
  * **不完整数据处理:** 处理接收到的不完整数据帧
  * **动态配置:** 帧检测规则在JSON中配置，动态执行
  * **缓冲区管理:** 采用固定大小循环缓冲区存储接收数据

### 2.4 数据解析需求
* **FR005: 基于偏移的数据解析**
  * **数据类型:** 支持int8/16/32、float32/64
  * **字节序:** 支持大端和小端配置
  * **缩放系数:** 支持乘法缩放（实际值 = 原始值 * 缩放系数）
  * **解析结构:** 不考虑嵌套结构，仅支持平面数据解析

### 2.5 协议流程配置需求
* **FR006: 动态协议流程**
  * **流程配置:** 每个协议的完整流程在JSON中配置
  * **标准流程:** 初始化配置 → 属性查询指令 → 发送循环查询指令
  * **自定义流程:** 支持协议特定的自定义流程
  * **流程控制:** 支持条件判断和错误处理

### 2.6 用户界面需求
* **FR007: 命令行界面（第一阶段）**
  * **交互式配置选择:** 程序启动后通过交互方式选择JSON配置文件
  * **控制台输出:** 解析后的数据在控制台实时显示
  * **文件输出:** 同时将数据输出到文件中保存
  
* **FR008: GUI界面（第二阶段）**
  * **PyQt界面:** 开发基于PyQt的图形用户界面
  * **文件选择:** 通过UI按钮打开文件加载JSON配置
  * **数据展示:** 图形化展示解析后的数据

### 2.7 日志系统需求
* **FR009: 分级日志系统**
  * **日志级别:** 支持DEBUG、INFO、WARNING、ERROR、CRITICAL
  * **本地保存:** 日志文件保存在本地，便于用户调试
  * **日志轮转:** 支持日志文件大小和时间轮转
  * **格式化输出:** 统一的日志格式，包含时间戳、级别、模块、消息
  * **主程序实现:** 日志系统在主程序中实现，不在JSON配置中定义

## 3. 系统架构需求

### 3.1 多线程架构
* **串口通信线程:** 独立线程处理串口数据收发
* **数据处理线程:** 独立线程处理数据解析和业务逻辑
* **主线程:** 处理用户界面和程序控制
* **线程同步:** 使用队列和锁机制确保线程安全

### 3.2 模块化设计
* **串口管理模块:** 封装pyserial，处理串口连接和通信
* **协议解析模块:** 动态加载JSON配置，解析协议规则
* **数据处理模块:** 处理数据流、帧检测、数据解析
* **配置管理模块:** JSON配置文件的加载、验证、管理
* **日志管理模块:** 统一的日志记录和管理
* **用户界面模块:** 命令行和GUI界面的实现

## 4. JSON配置文件结构规范

### 4.1 完整配置文件结构
```json
{
  "protocol_info": {
    "name": "自定义协议名称",
    "description": "协议描述",
    "version": "1.0"
  },
  "serial_config": {
    "baudrate": 9600,
    "databits": 8,
    "parity": "none",
    "stopbits": 1,
    "timeout": 1.0
  },
  "protocol_flow": {
    "steps": [
      {
        "name": "初始化配置",
        "type": "setup",
        "commands": ["init_cmd1", "init_cmd2"]
      },
      {
        "name": "属性查询",
        "type": "query", 
        "commands": ["query_cmd1"]
      },
      {
        "name": "循环查询激活",
        "type": "continuous_start",
        "commands": ["start_continuous"]
      }
    ]
  },
  "commands": {
    "setup": [
      {
        "id": "init_cmd1",
        "name": "初始化命令",
        "send": "01 03 00 00 00 01 84 0A",
        "response_validation": {
          "type": "exact",
          "pattern": "01 03 02 00 01 79 84",
          "timeout": 2.0,
          "retry_count": 3
        }
      }
    ],
    "query": [
      {
        "id": "query_cmd1",
        "name": "查询温度",
        "send": "01 03 00 01 00 01 D5 CA",
        "response_validation": {
          "type": "regex",
          "pattern": "01 03 02 [0-9A-F]{4} [0-9A-F]{4}",
          "timeout": 1.0,
          "retry_count": 2
        }
      }
    ],
    "continuous": [
      {
        "id": "start_continuous",
        "name": "启动循环查询",
        "send": "01 04 00 00 00 02 71 CB",
        "response_validation": {
          "type": "exact",
          "pattern": "01 04 04",
          "timeout": 1.0,
          "retry_count": 1
        }
      }
    ]
  },
  "continuous_data": {
    "frame_detection": {
      "header": "AA BB",
      "tail": "CC DD",
      "min_length": 8,
      "max_length": 256,
      "length_field": {
        "offset": 2,
        "length": 1,
        "includes_header": true
      }
    },
    "data_parsing": [
      {
        "name": "温度",
        "offset": 4,
        "length": 2,
        "data_type": "int16",
        "endian": "big",
        "scale_factor": 0.1,
        "unit": "°C"
      },
      {
        "name": "湿度",
        "offset": 6,
        "length": 2,
        "data_type": "int16",
        "endian": "big",
        "scale_factor": 0.01,
        "unit": "%RH"
      }
    ]
  }
}
```

## 5. 关键技术要求

### 5.1 数据流处理算法
* **固定大小循环缓冲区:** 使用固定大小的循环缓冲区管理串口接收数据
* **帧检测算法:** 基于帧头、帧尾、长度字段的完整帧检测
* **数据粘连处理:** 一次接收多个完整帧的分离处理
* **数据分片处理:** 不完整帧的缓存和重组处理
* **错误恢复机制:** 检测到错误帧时的数据流恢复策略

### 5.2 多线程通信机制
* **独立串口线程:** 串口数据收发在独立线程中执行
* **线程间数据传递:** 使用队列机制在线程间传递数据
* **线程同步控制:** 使用锁机制确保数据一致性
* **线程生命周期管理:** 程序启动和退出时的线程管理

### 5.3 动态配置加载
* **JSON配置验证:** 配置文件格式和内容的完整性验证
* **配置热加载:** 支持运行时重新加载配置文件
* **配置错误处理:** 配置文件错误时的友好提示和处理

## 6. 技术规范

### 6.1 开发环境
* **主要语言:** Python 3.8+
* **核心库:** 
  * pyserial（串口通信）
  * struct（二进制数据处理）
  * threading（多线程处理）
  * queue（线程间通信）
  * json（配置文件处理）
  * re（正则表达式）
  * logging（日志系统）
  * PyQt5/6（GUI界面，第二阶段）
* **打包工具:** PyInstaller

### 6.2 编码规范
* **核心编码原则:** KISS, YAGNI, SOLID, DRY, 高内聚低耦合, 代码可读性, 可测试性, 安全编码
* **命名约定:** 
  * 变量和函数：snake_case
  * 类名：PascalCase
  * 常量：UPPER_CASE
* **数据格式:** 十六进制字符串统一使用大写，空格分隔（如："01 03 00 00"）

### 6.3 错误处理规范
* **串口通信异常:** 分类处理连接失败、读写超时、数据格式错误
* **配置文件异常:** JSON格式错误、必需字段缺失、数据类型不匹配
* **数据解析异常:** 偏移越界、数据类型转换失败、缩放计算错误
* **通信失败处理:** 直接报错，提示用户具体错误信息

### 6.4 日志规范
* **日志级别使用:**
  * DEBUG: 详细的调试信息
  * INFO: 一般信息，如连接状态、数据接收
  * WARNING: 警告信息，如重试操作
  * ERROR: 错误信息，如通信失败
  * CRITICAL: 严重错误，如系统崩溃
* **日志格式:** `[时间戳] [级别] [模块] - 消息内容`
* **日志文件:** 按日期和大小轮转，保留最近30天的日志

## 7. 非功能性需求

### 7.1 性能要求
* **串口通信延迟:** 指令发送到接收应答的延迟 < 100ms
* **数据解析性能:** 连续数据解析速度 > 1000帧/秒
* **内存使用:** 程序运行内存占用 < 100MB
* **CPU使用:** 正常运行时CPU占用 < 10%

### 7.2 可靠性要求
* **通信稳定性:** 支持长时间连续运行（24小时+）
* **错误恢复:** 通信中断后自动重连和恢复
* **数据完整性:** 确保数据解析的准确性和完整性
* **线程安全:** 多线程环境下的数据一致性

### 7.3 可扩展性要求
* **协议扩展:** 架构支持未来添加modbus RTU等其他协议
* **配置扩展:** JSON配置结构支持新功能的扩展
* **模块化设计:** 各功能模块独立，便于维护和扩展
* **界面扩展:** 从命令行到GUI的平滑过渡

## 8. 开发阶段规划

### 8.1 第一阶段：核心功能实现
* **目标:** 实现完整的命令行版本
* **功能:** 串口通信、协议解析、数据处理、日志系统
* **交付:** 可独立运行的命令行程序

### 8.2 第二阶段：GUI界面开发
* **目标:** 开发PyQt图形用户界面
* **功能:** 可视化配置、数据展示、用户交互
* **交付:** 完整的GUI应用程序

## 9. 验收标准

### 9.1 功能验收
* **串口通信:** 能够正确发送十六进制指令并接收应答
* **应答验证:** 精确对比和正则表达式验证均正常工作
* **数据流处理:** 正确处理分片、粘连、不完整数据
* **数据解析:** 基于偏移的数据解析结果准确
* **动态配置:** JSON配置文件加载和验证功能正常
* **日志系统:** 分级日志正常记录和保存
* **程序打包:** PyInstaller打包的程序能够独立运行

### 9.2 质量验收
* **代码质量:** 遵循编码规范，代码可读性良好
* **测试覆盖:** 核心功能单元测试覆盖率 > 80%
* **错误处理:** 各种异常情况都有适当的处理机制
* **性能指标:** 满足所有性能要求
* **文档完整:** 代码注释和用户文档完整准确

## 10. 递进输出 (为模式2方案细化提供需求基础)

### 10.1 为BA角色提供的关键信息
| 输出类别 | 具体内容 | 方案设计影响 | 优先级 |
| :--- | :--- | :--- | :--- |
| 核心架构 | 多线程架构、固定缓冲区、模块化设计 | 系统架构设计的核心约束 | 高 |
| 技术约束 | pyserial、独立线程、JSON配置、日志系统 | 技术栈选择的限制条件 | 高 |
| 数据处理 | 帧检测算法、数据重组、完整性校验 | 数据处理算法设计 | 高 |
| 用户界面 | 命令行优先、GUI后续、交互式配置 | 用户体验设计方向 | 中 |
| 扩展需求 | 协议扩展、界面扩展、功能扩展 | 架构可扩展性设计 | 中 |

### 10.2 需要BA角色重点分析的技术方案
1. **固定大小循环缓冲区的具体实现方案**
2. **多线程架构的详细设计和线程间通信机制**
3. **帧检测算法的具体实现和优化策略**
4. **JSON配置文件的验证和错误处理机制**
5. **从命令行到GUI的架构演进方案**

**递进关系说明:** 本文档作为模式1的产出，为模式2的方案细化与多轮沟通提供完整的需求基础，确保BA角色能够基于准确的需求信息进行解决方案设计和技术方案对比分析。