# 1. 需求与规范文档 - 数据采集系统

| 版本 | 最后更新时间 | 更新者 | 变更摘要 |
| :--- | :--- | :--- | :--- |
| 1.0  | 2025-01-08 | PM | 初始创建 |
| 1.1  | 2025-01-08 | PM | 基于用户反馈的详细需求分析 |

---

## 1. 项目概述

### 1.1 项目目标
开发一个支持多种硬件协议的数据采集系统，具备高度的动态配置能力，通过JSON配置文件实现任意自由串口协议的支持，无需硬编码。

### 1.2 项目范围
* **范围内 (In Scope):**
  * 自由串口协议支持（优先开发）
  * modbus RTU协议支持（后续扩展）
  * 基于pyserial的串口通信
  * JSON配置文件动态加载
  * 三种工作模式：设置、单次查询、循环查询
  * 十六进制数据处理
  * 精确的指令应答验证
  * 基于偏移的数据解析
  * PyInstaller程序打包
* **范围外 (Out of Scope):**
  * 其他通信协议（如TCP/IP、USB等）
  * 复杂的嵌套数据结构解析
  * 图形化配置工具
  * 实时数据可视化界面

## 2. 详细功能需求

### 2.1 串口协议支持需求
* **FR001: 自由串口协议支持**
  * **描述:** 支持任意自定义的串口协议，无标准帧结构限制
  * **数据格式:** 全部使用十六进制字符串配置（如："01 03 00 00 00 01 84 0A"）
  * **帧结构:** 每种协议自定义，包含帧头、帧尾、校验数据、报文数据的完整指令
  * **字节序:** 支持大端(big)和小端(little)，在配置中指定

* **FR002: 三种工作模式**
  * **设置模式:** 向从机发送单次配置指令
  * **单次查询:** 向从机发送单次查询指令
  * **循环查询:** 从机持续向主机发送数据报文，软件进行解析

### 2.2 应答报文验证需求
* **FR003: 多种验证方式**
  * **精确对比:** 按字节完全匹配应答报文
  * **正则表达式:** 支持十六进制正则表达式匹配
  * **超时重试:** 可配置超时时间和重试次数
  * **失败处理:** 超过重试次数后继续后续处理

### 2.3 数据解析需求
* **FR004: 基于偏移的数据解析**
  * **数据类型:** 支持int8/16/32、float32/64
  * **字节序:** 支持大端和小端配置
  * **缩放系数:** 支持乘法缩放（实际值 = 原始值 * 缩放系数）
  * **解析结构:** 不考虑嵌套结构，仅支持平面数据解析

### 2.4 动态配置需求
* **FR005: JSON配置文件**
  * **动态加载:** 程序启动后通过读取JSON文件实现协议配置
  * **热更新:** 支持配置文件的动态重载
  * **配置验证:** 对JSON配置文件进行格式和内容验证

## 3. JSON配置文件结构规范

### 3.1 配置文件结构
```json
{
  "protocol_info": {
    "name": "自定义协议名称",
    "description": "协议描述",
    "version": "1.0"
  },
  "serial_config": {
    "baudrate": 9600,
    "databits": 8,
    "parity": "none",
    "stopbits": 1,
    "timeout": 1.0
  },
  "commands": {
    "setup": [
      {
        "name": "初始化命令",
        "send": "01 03 00 00 00 01 84 0A",
        "response_validation": {
          "type": "exact",
          "pattern": "01 03 02 00 01 79 84",
          "timeout": 2.0,
          "retry_count": 3
        }
      }
    ],
    "query": [
      {
        "name": "查询温度",
        "send": "01 03 00 01 00 01 D5 CA",
        "response_validation": {
          "type": "regex",
          "pattern": "01 03 02 [0-9A-F]{4} [0-9A-F]{4}",
          "timeout": 1.0,
          "retry_count": 2
        }
      }
    ]
  },
  "continuous_data": {
    "frame_detection": {
      "header": "AA BB",
      "tail": "CC DD",
      "min_length": 8,
      "max_length": 256
    },
    "data_parsing": [
      {
        "name": "温度",
        "offset": 4,
        "length": 2,
        "data_type": "int16",
        "endian": "big",
        "scale_factor": 0.1,
        "unit": "°C"
      }
    ]
  }
}
```

## 4. 技术规范

### 4.1 开发环境
* **主要语言:** Python 3.8+
* **核心库:** pyserial（串口通信）
* **数据处理:** struct（二进制数据处理）
* **多线程:** threading（并发处理）
* **配置解析:** json（配置文件处理）
* **打包工具:** PyInstaller

### 4.2 编码规范
* **核心编码原则:** KISS, YAGNI, SOLID, DRY, 高内聚低耦合, 代码可读性, 可测试性, 安全编码
* **命名约定:** 
  * 变量和函数：snake_case
  * 类名：PascalCase
  * 常量：UPPER_CASE
* **数据格式:** 十六进制字符串统一使用大写，空格分隔（如："01 03 00 00"）

### 4.3 错误处理规范
* **串口通信异常:** 分类处理连接失败、读写超时、数据格式错误
* **配置文件异常:** JSON格式错误、必需字段缺失、数据类型不匹配
* **数据解析异常:** 偏移越界、数据类型转换失败、缩放计算错误

## 5. 非功能性需求

### 5.1 性能要求
* **串口通信延迟:** 指令发送到接收应答的延迟 < 100ms
* **数据解析性能:** 连续数据解析速度 > 1000帧/秒
* **内存使用:** 程序运行内存占用 < 100MB

### 5.2 可靠性要求
* **通信稳定性:** 支持长时间连续运行（24小时+）
* **错误恢复:** 通信中断后自动重连和恢复
* **数据完整性:** 确保数据解析的准确性和完整性

### 5.3 可扩展性要求
* **协议扩展:** 架构支持未来添加modbus RTU等其他协议
* **配置扩展:** JSON配置结构支持新功能的扩展
* **模块化设计:** 各功能模块独立，便于维护和扩展

## 6. 验收标准

### 6.1 功能验收
* **串口通信:** 能够正确发送十六进制指令并接收应答
* **应答验证:** 精确对比和正则表达式验证均正常工作
* **数据解析:** 基于偏移的数据解析结果准确
* **动态配置:** JSON配置文件加载和验证功能正常
* **程序打包:** PyInstaller打包的程序能够独立运行

### 6.2 质量验收
* **代码质量:** 遵循编码规范，代码可读性良好
* **测试覆盖:** 核心功能单元测试覆盖率 > 80%
* **错误处理:** 各种异常情况都有适当的处理机制
* **文档完整:** 代码注释和用户文档完整准确

## 7. 递进输出 (为模式2方案细化提供需求基础)

### 7.1 为BA角色提供的关键信息
| 输出类别 | 具体内容 | 方案设计影响 | 优先级 |
| :--- | :--- | :--- | :--- |
| 核心功能 | 三种工作模式、动态配置、数据解析 | 系统架构设计的核心约束 | 高 |
| 技术约束 | pyserial、十六进制处理、JSON配置 | 技术栈选择的限制条件 | 高 |
| 性能要求 | 通信延迟、解析性能、内存占用 | 架构设计的性能目标 | 高 |
| 扩展需求 | 支持modbus RTU、模块化设计 | 架构可扩展性设计 | 中 |
| 用户体验 | 动态配置、错误处理、程序打包 | 用户界面和交互设计 | 中 |

### 7.2 待BA角色深入分析的问题
1. **循环查询的数据流处理机制**
2. **用户界面需求（GUI vs 命令行）**
3. **多线程架构设计方案**
4. **配置文件验证和错误提示机制**
5. **日志记录和调试功能需求**

**递进关系说明:** 本文档作为模式1的产出，为模式2的方案细化与多轮沟通提供完整的需求基础，确保BA角色能够基于准确的需求信息进行解决方案设计和技术方案对比分析。